<script lang="ts">
import { defineComponent, PropType } from 'vue';
import { EKSConfig, EKSNodeGroup, NormanCluster } from '../../types';
import { LabeledInput } from '@components/Form/LabeledInput';
import Banner from '@components/Banner/Banner.vue';

export default defineComponent({
  name: 'ConfigSummary',

  components: {
    LabeledInput,
    Banner
  },

  props: {
    normanCluster: {
      type:     Object as PropType<NormanCluster>,
      required: true
    },
    config: {
      type:     Object as PropType<EKSConfig>,
      required: true
    },
    nodeGroups: {
      type:    Array as PropType<EKSNodeGroup[]>,
      default: () => []
    },
    region: {
      type:    String,
      default: ''
    }
  },

  computed: {
    clusterName(): string {
      return this.normanCluster?.name || 'Not set';
    },

    awsRegion(): string {
      return this.config?.region || this.region || 'Not set';
    },

    kubernetesVersion(): string {
      return this.config?.kubernetesVersion || 'Latest stable';
    },

    totalNodes(): { min: number; max: number; desired: number } {
      const totals = this.nodeGroups.reduce((acc, group) => {
        return {
          min:     acc.min + (group.minSize || 0),
          max:     acc.max + (group.maxSize || 0),
          desired: acc.desired + (group.desiredSize || 0)
        };
      }, { min: 0, max: 0, desired: 0 });

      return totals;
    },

    primaryNodeGroup(): EKSNodeGroup | null {
      return this.nodeGroups[0] || null;
    },

    estimatedMonthlyCost(): string {
      // Simple cost estimation
      const costMap: Record<string, number> = {
        't3.small':   15,
        't3.medium':  30,
        't3.large':   60,
        't3.xlarge':  120,
        't4g.small':  12,
        't4g.medium': 24,
        't4g.large':  48,
        'm5.large':   70,
        'm5.xlarge':  140,
        'm6i.large':  70,
        'm6i.xlarge': 140,
      };

      let totalCost = 0;
      this.nodeGroups.forEach(group => {
        const instanceCost = group.instanceType ? costMap[group.instanceType] || 50 : 50;
        totalCost += instanceCost * (group.desiredSize || 2);
      });

      // Add EKS control plane cost ($0.10/hour = ~$73/month)
      totalCost += 73;

      return `$${totalCost}`;
    },

    networkingMode(): string {
      if (this.config?.publicAccess && !this.config?.privateAccess) {
        return 'Public';
      } else if (!this.config?.publicAccess && this.config?.privateAccess) {
        return 'Private';
      } else if (this.config?.publicAccess && this.config?.privateAccess) {
        return 'Public and Private';
      }
      return 'Default (Public)';
    }
  }
});
</script>

<template>
  <div class="config-summary">
    <div class="summary-section">
      <h3>
        <i class="icon icon-cluster" />
        Cluster Configuration
      </h3>
      
      <div class="summary-grid">
        <LabeledInput
          label="Cluster Name"
          :value="clusterName"
          mode="view"
          class="summary-item"
        />

        <LabeledInput
          label="AWS Region"
          :value="awsRegion"
          mode="view"
          class="summary-item"
        />

        <LabeledInput
          label="Kubernetes Version"
          :value="kubernetesVersion"
          mode="view"
          class="summary-item"
        />

        <LabeledInput
          label="Network Access"
          :value="networkingMode"
          mode="view"
          class="summary-item"
        />
      </div>
    </div>

    <div class="summary-section mt-20">
      <h3>
        <i class="icon icon-nodes" />
        Node Configuration
      </h3>
      
      <div
        v-if="primaryNodeGroup"
        class="node-summary"
      >
        <div class="summary-grid">
          <LabeledInput
            label="Instance Type"
            :value="primaryNodeGroup.instanceType"
            mode="view"
            class="summary-item"
          />

          <LabeledInput
            label="Disk Size"
            :value="`${primaryNodeGroup.diskSize} GB`"
            mode="view"
            class="summary-item"
          />

          <LabeledInput
            label="Auto-scaling"
            :value="`${totalNodes.min} - ${totalNodes.max} nodes`"
            mode="view"
            class="summary-item"
          />

          <LabeledInput
            label="Initial Size"
            :value="`${totalNodes.desired} nodes`"
            mode="view"
            class="summary-item"
          />
        </div>
      </div>

      <div
        v-if="nodeGroups.length > 1"
        class="mt-10"
      >
        <Banner color="info">
          <p>{{ nodeGroups.length }} node groups configured</p>
        </Banner>
      </div>
    </div>

    <div class="summary-section mt-20 cost-section">
      <h3>
        <i class="icon icon-dollar" />
        Estimated Cost
      </h3>
      
      <div class="cost-breakdown">
        <div class="cost-main">
          <span class="cost-label">Monthly estimate:</span>
          <span class="cost-value">{{ estimatedMonthlyCost }}</span>
        </div>
        <p class="cost-disclaimer">
          * This is a rough estimate based on instance types and does not include data transfer, storage, or other AWS services.
        </p>
      </div>
    </div>

    <div class="summary-section mt-20">
      <Banner
        color="success"
        class="ready-banner"
      >
        <div class="ready-content">
          <i class="icon icon-checkmark icon-2x" />
          <div>
            <h4>Ready to create your cluster!</h4>
            <p>Your cluster will be created with production-ready defaults including:</p>
            <ul>
              <li>Automatic security updates</li>
              <li>Network isolation and security groups</li>
              <li>CloudWatch logging enabled</li>
              <li>IAM roles for service accounts (IRSA)</li>
            </ul>
          </div>
        </div>
      </Banner>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.config-summary {
  .summary-section {
    padding: 20px;
    background: var(--body-bg);
    border: 1px solid var(--border);
    border-radius: var(--border-radius);

    h3 {
      margin: 0 0 20px 0;
      font-size: 18px;
      font-weight: 600;
      display: flex;
      align-items: center;
      gap: 10px;
      color: var(--text-default);

      i {
        color: var(--primary);
      }
    }
  }

  .summary-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;

    .summary-item {
      padding: 10px;
      background: var(--nav-bg);
      border-radius: var(--border-radius);

      ::v-deep .labeled-input {
        label {
          color: var(--text-muted);
          font-size: 12px;
          text-transform: uppercase;
          margin-bottom: 5px;
        }

        input {
          color: var(--text-default);
          font-size: 16px;
          font-weight: 500;
          background: transparent;
          border: none;
          padding: 0;
        }
      }
    }
  }

  .cost-section {
    background: linear-gradient(135deg, var(--body-bg) 0%, var(--nav-bg) 100%);
  }

  .cost-breakdown {
    .cost-main {
      display: flex;
      align-items: baseline;
      gap: 15px;
      margin-bottom: 10px;

      .cost-label {
        font-size: 16px;
        color: var(--text-muted);
      }

      .cost-value {
        font-size: 32px;
        font-weight: 600;
        color: var(--success);
      }
    }

    .cost-disclaimer {
      font-size: 12px;
      color: var(--text-muted);
      font-style: italic;
      margin: 0;
    }
  }

  .ready-banner {
    .ready-content {
      display: flex;
      gap: 20px;
      align-items: flex-start;

      i {
        color: var(--success);
        flex-shrink: 0;
      }

      h4 {
        margin: 0 0 10px 0;
        font-size: 18px;
        font-weight: 600;
      }

      p {
        margin: 0 0 10px 0;
      }

      ul {
        margin: 0;
        padding-left: 20px;

        li {
          margin: 5px 0;
        }
      }
    }
  }
}
</style>
